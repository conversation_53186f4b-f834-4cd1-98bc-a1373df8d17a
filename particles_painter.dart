import 'dart:math' as math;
import 'package:flutter/material.dart';

class ParticlesPainter extends CustomPainter {
  final double progress;
  final List<Particle> particles = [];
  final int particleCount = 50;
  final Random random = Random();

  ParticlesPainter({required this.progress}) {
    if (particles.isEmpty) {
      _initParticles();
    }
  }

  void _initParticles() {
    for (int i = 0; i < particleCount; i++) {
      particles.add(Particle(random));
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeCap = StrokeCap.round;

    for (var particle in particles) {
      // Update particle position based on animation progress
      final x = (particle.initialX + particle.speed * math.cos(particle.angle) * progress) % size.width;
      final y = (particle.initialY + particle.speed * math.sin(particle.angle) * progress) % size.height;
      
      // Draw particle with size based on its z-position
      paint.color = Colors.white.withOpacity(particle.opacity);
      canvas.drawCircle(
        Offset(x, y),
        particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant ParticlesPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

class Particle {
  late double initialX;
  late double initialY;
  late double angle;
  late double speed;
  late double size;
  late double opacity;
  
  Particle(Random random) {
    initialX = random.nextDouble() * 1000;
    initialY = random.nextDouble() * 1000;
    angle = random.nextDouble() * 2 * math.pi;
    speed = 20 + random.nextDouble() * 30;
    size = 1 + random.nextDouble() * 2;
    opacity = 0.1 + random.nextDouble() * 0.4;
  }
}

class Random {
  final math.Random _random = math.Random();
  
  double nextDouble() {
    return _random.nextDouble();
  }
}
