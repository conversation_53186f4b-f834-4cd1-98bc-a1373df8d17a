import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:attendo_app/screens/CheckInSuccessScreen.dart';
import 'package:attendo_app/screens/announcement.dart';
import 'package:attendo_app/screens/profile_screen.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_swipe_button/flutter_swipe_button.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:iconly/iconly.dart';
import 'package:google_fonts/google_fonts.dart';

import 'about_developers.dart';

class HomeAttendanceScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeAttendanceScreen> {
  // State variables remain the same
  final List<String> dates = ["06", "07", "08", "09"];
  final List<String> days = ["Thu", "Fri", "Sat", "Sun"];
  Map<String, dynamic>? employeeData;
  Map<String, dynamic>? todayAttendance;
  String checkInTime = ".. : ..";
  String checkOutTime = ".. : ..";
  String breakTime = ".. : ..";
  int totalDays = 26;
  bool isCheckedIn = false;
  String? username;
  String? jobTitle;
  String? imageUrl;
  final String baseUrl = 'https://attendance-system-production-4afd.up.railway.app';

  // Methods remain the same
  String formatTime(String timeString) {
    final timeUtc = DateTime.parse(timeString);
    final localTime = timeUtc.toLocal();
    return DateFormat.jm().format(localTime);
  }

  @override
  void initState() {
    super.initState();
    debugPrint("✅ initState triggered");
    loadData();
    fetchEmployeeProfile();
  }

  // All your existing methods remain unchanged
  Future<void> fetchEmployeeProfile() async {
    // Existing implementation
  }

  Future<void> loadData() async {
    // Existing implementation
  }

  Future<void> fetchTodayAttendance() async {
    // Existing implementation
  }

  Future<Map<String, dynamic>?> fetchEmployeeByDeviceID(String deviceID) async {
    // Existing implementation
  }

  Future<Map<String, dynamic>?> getTodayAttendanceRecord(String deviceID) async {
    // Existing implementation
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Color(0xFF3D5CFF);
    final secondaryColor = Color(0xFF6C8EFF);
    final backgroundColor = isDarkMode ? Color(0xFF121212) : Color(0xFFF8F9FD);
    final cardColor = isDarkMode ? Color(0xFF1E1E1E) : Colors.white;
    final textColor = isDarkMode ? Colors.white : Color(0xFF333333);
    final subtitleColor = isDarkMode ? Colors.grey[400] : Colors.grey[600];

    return Scaffold(
      backgroundColor: backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // App Bar
              Padding(
                padding: EdgeInsets.fromLTRB(20, 20, 20, 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => ProfileScreen()),
                        );
                      },
                      child: Row(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(color: primaryColor, width: 2),
                            ),
                            child: CircleAvatar(
                              radius: 22,
                              backgroundImage: imageUrl != null
                                  ? NetworkImage(imageUrl!)
                                  : AssetImage('assets/hasina.jpg') as ImageProvider,
                            ),
                          ),
                          SizedBox(width: 12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                username ?? "Welcome",
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: textColor,
                                ),
                              ),
                              Text(
                                jobTitle ?? "Loading...",
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  color: subtitleColor,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: cardColor,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode ? Colors.black12 : Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: IconButton(
                            icon: Icon(IconlyLight.notification, color: primaryColor),
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (context) => const AnnouncementsScreen()),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Date display
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [primaryColor, secondaryColor],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: primaryColor.withOpacity(0.3),
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            DateFormat('EEEE').format(DateTime.now()),
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            DateFormat('MMMM d, yyyy').format(DateTime.now()),
                            style: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      Icon(
                        IconlyBold.calendar,
                        color: Colors.white,
                        size: 32,
                      ),
                    ],
                  ),
                ),
              ),

              // Today's Attendance Section
              Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Today's Attendance",
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: textColor,
                      ),
                    ),
                    SizedBox(height: 15),
                    GridView.count(
                      crossAxisCount: 2,
                      shrinkWrap: true,
                      crossAxisSpacing: 15,
                      mainAxisSpacing: 15,
                      childAspectRatio: 1.2,
                      physics: NeverScrollableScrollPhysics(),
                      children: [
                        ModernAttendanceCard(
                          title: "Check In",
                          time: checkInTime,
                          subtitle: "On Progress",
                          icon: IconlyBold.login,
                          color: Color(0xFF4CAF50),
                          isDarkMode: isDarkMode,
                        ),
                        ModernAttendanceCard(
                          title: "Check Out",
                          time: checkOutTime,
                          subtitle: "Done The Day",
                          icon: IconlyBold.logout,
                          color: Color(0xFF2196F3),
                          isDarkMode: isDarkMode,
                        ),
                        ModernAttendanceCard(
                          title: "Break Time",
                          time: "00:30 min",
                          subtitle: "Avg Time 30 min",
                          icon: IconlyBold.time_circle,
                          color: Color(0xFFFF9800),
                          isDarkMode: isDarkMode,
                        ),
                        ModernAttendanceCard(
                          title: "Total Days",
                          time: "26",
                          subtitle: "Working Days",
                          icon: IconlyBold.calendar,
                          color: Color(0xFF9C27B0),
                          isDarkMode: isDarkMode,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Activity Section
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Your Activity",
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: textColor,
                      ),
                    ),
                    SizedBox(height: 15),
                    ModernActivityCard(
                      title: "Check In",
                      time: checkInTime,
                      date: DateFormat('MMMM d, yyyy').format(DateTime.now()),
                      status: "On Time",
                      icon: IconlyBold.login,
                      color: Color(0xFF4CAF50),
                      isDarkMode: isDarkMode,
                    ),
                    ModernActivityCard(
                      title: "Check Out",
                      time: checkOutTime,
                      date: DateFormat('MMMM d, yyyy').format(DateTime.now()),
                      status: "On Time",
                      icon: IconlyBold.logout,
                      color: Color(0xFF2196F3),
                      isDarkMode: isDarkMode,
                    ),
                  ],
                ),
              ),

              // Swipe Button
              Padding(
                padding: EdgeInsets.all(20),
                child: ModernSwipeButton(isCheckedIn: isCheckedIn),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Modern Activity Card Widget
class ModernActivityCard extends StatelessWidget {
  final String title;
  final String time;
  final String date;
  final String status;
  final IconData icon;
  final Color color;
  final bool isDarkMode;

  ModernActivityCard({
    required this.title,
    required this.time,
    required this.date,
    required this.status,
    required this.icon,
    required this.color,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    final cardColor = isDarkMode ? Color(0xFF1E1E1E) : Colors.white;
    final textColor = isDarkMode ? Colors.white : Color(0xFF333333);
    final subtitleColor = isDarkMode ? Colors.grey[400] : Colors.grey[600];

    return Container(
      padding: EdgeInsets.all(16),
      margin: EdgeInsets.only(bottom: 15),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode ? Colors.black12 : Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
                Text(
                  date,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: subtitleColor,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                time,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: textColor,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  status,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Modern Attendance Card Widget
class ModernAttendanceCard extends StatelessWidget {
  final String title;
  final String time;
  final String subtitle;
  final IconData icon;
  final Color color;
  final bool isDarkMode;

  const ModernAttendanceCard({
    required this.title,
    required this.time,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    final cardColor = isDarkMode ? Color(0xFF1E1E1E) : Colors.white;
    final textColor = isDarkMode ? Colors.white : Color(0xFF333333);
    final subtitleColor = isDarkMode ? Colors.grey[400] : Colors.grey[600];

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode ? Colors.black12 : Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          SizedBox(height: 12),
          Text(
            time,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
          SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
          ),
          Text(
            subtitle,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: subtitleColor,
            ),
          ),
        ],
      ),
    );
  }
}

// Modern Swipe Button
class ModernSwipeButton extends StatelessWidget {
  final bool isCheckedIn;

  const ModernSwipeButton({required this.isCheckedIn});

  @override
  Widget build(BuildContext context) {
    final color = isCheckedIn ? Color(0xFF2196F3) : Color(0xFF4CAF50);
    final label = isCheckedIn ? "Swipe to Check Out" : "Swipe to Check In";
    final icon = isCheckedIn ? IconlyBold.logout : IconlyBold.login;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: SwipeButton.expand(
        thumb: Container(
          margin: EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        thumbPadding: EdgeInsets.all(0),
        borderRadius: BorderRadius.circular(16),
        activeThumbColor: Colors.white,
        activeTrackColor: color,
        height: 60,
        child: Text(
          label,
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        onSwipe: () {
          Navigator.pushNamed(context, '/qr_code');
        },
      ),
    );
  }
}