import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/cupertino.dart';
import './theme_provider.dart';

// Define app colors
class AppColors {
  static const Color lightGrayishBlue = Color(0xFF9EB2BF);
  static const Color brightBlue = Color(0xFF209ACF);
  static const Color similarBrightBlue = Color(0xFF219CD0);
  static const Color darkGray = Color(0xFF272727);

  // Dark mode colors
  static const Color darkBackground = Color(0xFF272727);
  static const Color darkCardBackground = Color(0xFF333333);
  static const Color darkTextPrimary = Colors.white;
  static const Color darkTextSecondary = Color(0xFF9EB2BF);

  // Light mode colors
  static const Color lightBackground = Color(0xFFF5F7FA);
  static const Color lightCardBackground = Colors.white;
  static const Color lightTextPrimary = Color(0xFF272727);
  static const Color lightTextSecondary = Color(0xFF64748B);
}

class FilterBottomSheet extends StatefulWidget {
  final bool? isDarkMode;
  final Color? cardColor;
  final Color? textPrimaryColor;
  final Color? textSecondaryColor;

  const FilterBottomSheet({
    Key? key,
    this.isDarkMode,
    this.cardColor,
    this.textPrimaryColor,
    this.textSecondaryColor,
  }) : super(key: key);

  @override
  _FilterBottomSheetState createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> with TickerProviderStateMixin {
  bool isApproved = false;
  bool isUnapproved = false;
  bool isPending = false;
  bool isSickLeave = false;
  bool isPlannedLeave = false;
  bool isHoliday = false;
  String selectedTeamMember = "Select Team Member";
  List<String> teamMembers = ["Select Team Member", "Alexa Williams", "John Doe", "Martin Deo"];

  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _resetFilters() {
    setState(() {
      isApproved = false;
      isUnapproved = false;
      isPending = false;
      isSickLeave = false;
      isPlannedLeave = false;
      isHoliday = false;
      selectedTeamMember = "Select Team Member";
    });
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = widget.isDarkMode ?? themeProvider.isDarkMode;
    final cardColor = widget.cardColor ?? (isDarkMode ? AppColors.darkCardBackground : AppColors.lightCardBackground);
    final textPrimaryColor = widget.textPrimaryColor ?? (isDarkMode ? AppColors.darkTextPrimary : AppColors.lightTextPrimary);
    final textSecondaryColor = widget.textSecondaryColor ?? (isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary);

    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * MediaQuery.of(context).size.height),
          child: Container(
            decoration: BoxDecoration(
              color: cardColor,
              borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(isDarkMode ? 0.3 : 0.1),
                  blurRadius: 20,
                  offset: Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  margin: EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.lightGrayishBlue.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Header
                Padding(
                  padding: EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.brightBlue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          CupertinoIcons.slider_horizontal_3,
                          color: AppColors.brightBlue,
                          size: 20,
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          "Filter Options",
                          style: GoogleFonts.poppins(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                            color: textPrimaryColor,
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: Container(
                          padding: EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.lightGrayishBlue.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            CupertinoIcons.xmark,
                            color: AppColors.lightGrayishBlue,
                            size: 18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Status Section
                        _buildSectionHeader("Status", CupertinoIcons.checkmark_circle, textPrimaryColor),
                        SizedBox(height: 12),
                        _buildStatusSection(isDarkMode, textPrimaryColor),

                        SizedBox(height: 24),

                        // Leave Type Section
                        _buildSectionHeader("Leave Type", CupertinoIcons.tag, textPrimaryColor),
                        SizedBox(height: 12),
                        _buildLeaveTypeSection(isDarkMode, textPrimaryColor),

                        SizedBox(height: 32),

                        // Action Buttons
                        _buildActionButtons(isDarkMode),

                        SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, Color textColor) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppColors.brightBlue,
          size: 18,
        ),
        SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: textColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusSection(bool isDarkMode, Color textPrimaryColor) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode
          ? AppColors.lightGrayishBlue.withOpacity(0.05)
          : AppColors.lightGrayishBlue.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.lightGrayishBlue.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildCustomCheckbox("Approved", isApproved, Colors.green, (value) {
            setState(() => isApproved = value);
          }, textPrimaryColor),
          SizedBox(height: 8),
          _buildCustomCheckbox("Rejected", isUnapproved, Colors.red, (value) {
            setState(() => isUnapproved = value);
          }, textPrimaryColor),
          SizedBox(height: 8),
          _buildCustomCheckbox("Pending", isPending, Colors.orange, (value) {
            setState(() => isPending = value);
          }, textPrimaryColor),
        ],
      ),
    );
  }

  Widget _buildLeaveTypeSection(bool isDarkMode, Color textPrimaryColor) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode
          ? AppColors.lightGrayishBlue.withOpacity(0.05)
          : AppColors.lightGrayishBlue.withOpacity(0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.lightGrayishBlue.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildCustomCheckbox("Medical Leave", isSickLeave, AppColors.brightBlue, (value) {
            setState(() => isSickLeave = value);
          }, textPrimaryColor),
          SizedBox(height: 8),
          _buildCustomCheckbox("Casual Leave", isPlannedLeave, AppColors.similarBrightBlue, (value) {
            setState(() => isPlannedLeave = value);
          }, textPrimaryColor),
          SizedBox(height: 8),
          _buildCustomCheckbox("Holiday", isHoliday, AppColors.lightGrayishBlue, (value) {
            setState(() => isHoliday = value);
          }, textPrimaryColor),
        ],
      ),
    );
  }

  Widget _buildCustomCheckbox(String title, bool value, Color accentColor, Function(bool) onChanged, Color textPrimaryColor) {
    return GestureDetector(
      onTap: () => onChanged(!value),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          color: value
            ? accentColor.withOpacity(0.1)
            : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: value
            ? Border.all(color: accentColor, width: 1.5)
            : Border.all(color: Colors.transparent, width: 1.5),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: value ? accentColor : Colors.transparent,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: value ? accentColor : AppColors.lightGrayishBlue.withOpacity(0.5),
                  width: 2,
                ),
              ),
              child: value
                ? Icon(
                    CupertinoIcons.check_mark,
                    color: Colors.white,
                    size: 12,
                  )
                : null,
            ),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: value ? FontWeight.w600 : FontWeight.w500,
                  color: value ? accentColor : textPrimaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(bool isDarkMode) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 50,
            child: ElevatedButton(
              onPressed: _resetFilters,
              style: ElevatedButton.styleFrom(
                backgroundColor: isDarkMode
                  ? AppColors.lightGrayishBlue.withOpacity(0.2)
                  : AppColors.lightGrayishBlue.withOpacity(0.1),
                foregroundColor: AppColors.lightGrayishBlue,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: AppColors.lightGrayishBlue.withOpacity(0.3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    CupertinoIcons.refresh,
                    size: 18,
                    color: AppColors.lightGrayishBlue,
                  ),
                  SizedBox(width: 8),
                  Text(
                    "Reset",
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.lightGrayishBlue,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          flex: 2,
          child: Container(
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.brightBlue.withOpacity(0.3),
                  blurRadius: 8,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.brightBlue,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    CupertinoIcons.checkmark_alt,
                    size: 18,
                    color: Colors.white,
                  ),
                  SizedBox(width: 8),
                  Text(
                    "Apply Filters",
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
