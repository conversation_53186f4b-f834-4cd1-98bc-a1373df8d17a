import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import './theme_provider.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutDeveloperScreen extends StatelessWidget {
  const AboutDeveloperScreen({Key? key}) : super(key: key);

  // Function to launch URLs
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  // Function to make phone calls
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri uri = Uri(scheme: 'tel', path: phoneNumber);
    if (!await launchUrl(uri)) {
      throw Exception('Could not launch $uri');
    }
  }

  // Function to open WhatsApp
  Future<void> _openWhatsApp(String phoneNumber) async {
    final Uri uri = Uri.parse('https://wa.me/$phoneNumber');
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch WhatsApp');
    }
  }

  // Function to open Instagram
  Future<void> _openInstagram(String username) async {
    final Uri uri = Uri.parse('https://instagram.com/$username');
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch Instagram');
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    // Define colors based on your color palette
    final Color primaryColor = Color(0xFF209ACF); // Bright blue
    final Color secondaryColor = Color(0xFF219CD0); // Similar bright blue
    final Color accentColor = Color(0xFF9EB2BF); // Light grayish blue
    final Color darkColor = Color(0xFF272727); // Dark gray/almost black

    // Theme-specific colors
    final backgroundColor = isDarkMode ? darkColor : Colors.white;
    final cardColor = isDarkMode ? Color(0xFF333333) : Color(0xFFF5F5F5);
    final primaryTextColor = isDarkMode ? Colors.white : darkColor;
    final secondaryTextColor = isDarkMode ? accentColor : Color(0xFF666666);

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        backgroundColor: primaryColor,
        elevation: 0,
        title: Text(
          "About Developer",
          style: GoogleFonts.montserrat(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header with wave design
            Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.bottomCenter,
              children: [
                // Wave background
                Container(
                  height: 150,
                  decoration: BoxDecoration(
                    color: primaryColor,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(0),
                      bottomRight: Radius.circular(0),
                    ),
                  ),
                  child: ClipPath(
                    clipper: WaveClipper(),
                    child: Container(
                      height: 150,
                      decoration: BoxDecoration(
                        color: secondaryColor,
                      ),
                    ),
                  ),
                ),

                // Logo positioned over the wave
                Positioned(
                  bottom: -50,
                  child: Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: CircleAvatar(
                      radius: 60,
                      backgroundColor: Colors.white,
                      backgroundImage: AssetImage('assets/dev_logo.png'),
                    ),
                  ),
                ),
              ],
            ),

            // Company name and subtitle
            SizedBox(height: 60),
            Text(
              'Recent IT',
              style: GoogleFonts.montserrat(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
            SizedBox(height: 5),
            Text(
              'Developer of Attendo',
              style: GoogleFonts.montserrat(
                fontSize: 16,
                color: secondaryTextColor,
              ),
            ),

            SizedBox(height: 30),

            // About section
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: cardColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: primaryColor,
                        ),
                        SizedBox(width: 10),
                        Text(
                          'About Us',
                          style: GoogleFonts.montserrat(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: primaryColor,
                          ),
                        ),
                      ],
                    ),
                    Divider(color: accentColor.withOpacity(0.3)),
                    SizedBox(height: 10),
                    Text(
                      'Attendo is a smart attendance management system produced by Recent IT, a technology company founded in November 2023.',
                      style: GoogleFonts.montserrat(
                        fontSize: 14,
                        height: 1.5,
                        color: primaryTextColor,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text(
                      'Attendo simplifies daily attendance tracking with seamless check-ins, leave management, and insightful analytics.',
                      style: GoogleFonts.montserrat(
                        fontSize: 14,
                        height: 1.5,
                        color: primaryTextColor,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text(
                      'Recent IT is committed to delivering high-quality digital solutions that empower organizations in Somalia and beyond. The app is designed with a focus on user experience, security, and efficiency — helping teams stay organized and productive every day.',
                      style: GoogleFonts.montserrat(
                        fontSize: 14,
                        height: 1.5,
                        color: primaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 20),

            // Contact section
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: cardColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.contact_phone,
                          color: primaryColor,
                        ),
                        SizedBox(width: 10),
                        Text(
                          'Connect With Us',
                          style: GoogleFonts.montserrat(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: primaryColor,
                          ),
                        ),
                      ],
                    ),
                    Divider(color: accentColor.withOpacity(0.3)),
                    SizedBox(height: 15),

                    // Social media buttons in a grid
                    GridView.count(
                      crossAxisCount: 2,
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      childAspectRatio: 2.5,
                      mainAxisSpacing: 15,
                      crossAxisSpacing: 15,
                      children: [
                        _buildContactButton(
                          icon: Icons.call,
                          label: 'Call Us',
                          color: primaryColor,
                          isDarkMode: isDarkMode,
                          onTap: () => _makePhoneCall('+252615301507'),
                        ),
                        _buildContactButton(
                          icon: Icons.language,
                          label: 'Website',
                          color: secondaryColor,
                          isDarkMode: isDarkMode,
                          onTap: () => _launchUrl('https://recentit.so'),
                        ),
                        _buildContactButton(
                          icon: FontAwesomeIcons.whatsapp,
                          label: 'WhatsApp',
                          color: primaryColor,
                          isDarkMode: isDarkMode,
                          onTap: () => _openWhatsApp('252615301507'),
                        ),
                        _buildContactButton(
                          icon: FontAwesomeIcons.instagram,
                          label: 'Instagram',
                          color: secondaryColor,
                          isDarkMode: isDarkMode,
                          onTap: () => _openInstagram('recentit.so'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 30),

            // Footer
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 15),
              color: primaryColor,
              child: Column(
                children: [
                  Text(
                    '© 2023 Recent IT',
                    style: GoogleFonts.montserrat(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 5),
                  Text(
                    'v1.0.0',
                    style: GoogleFonts.montserrat(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactButton({
    required IconData icon,
    required String label,
    required Color color,
    required bool isDarkMode,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        decoration: BoxDecoration(
          color: isDarkMode ? color.withOpacity(0.2) : color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            SizedBox(width: 10),
            Expanded(
              child: Text(
                label,
                style: GoogleFonts.montserrat(
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : Color(0xFF272727),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
