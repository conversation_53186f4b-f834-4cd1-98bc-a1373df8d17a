import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/cupertino.dart';

import './theme_provider.dart';
import 'apply_leave_screen.dart';
import 'filter_bottom_sheet.dart';

// Define app colors
class AppColors {
  static const Color lightGrayishBlue = Color(0xFF9EB2BF);
  static const Color brightBlue = Color(0xFF209ACF);
  static const Color similarBrightBlue = Color(0xFF219CD0);
  static const Color darkGray = Color(0xFF272727);

  // Dark mode colors
  static const Color darkBackground = Color(0xFF272727);
  static const Color darkCardBackground = Color(0xFF333333);
  static const Color darkTextPrimary = Colors.white;
  static const Color darkTextSecondary = Color(0xFF9EB2BF);

  // Light mode colors
  static const Color lightBackground = Color(0xFFF5F7FA);
  static const Color lightCardBackground = Colors.white;
  static const Color lightTextPrimary = Color(0xFF272727);
  static const Color lightTextSecondary = Color(0xFF64748B);
}

class LeaveScreen extends StatefulWidget {
  @override
  _LeaveScreenState createState() => _LeaveScreenState();
}

class _LeaveScreenState extends State<LeaveScreen> with TickerProviderStateMixin {
  int selectedTab = 0;
  int pendingCount = 0;
  int approvedCount = 0;
  int rejectedCount = 0;
  List<Map<String, dynamic>> pendingLeaves = [];
  List<Map<String, dynamic>> approvedLeaves = [];
  List<Map<String, dynamic>> rejectedLeaves = [];
  List<dynamic> leaveRequests = [];
  bool isLoading = false;

  final String baseUrl = 'https://attendance-system-production-4afd.up.railway.app';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(begin: Offset(0, 0.3), end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    fetchLeaveRequests();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> fetchLeaveRequests() async {
    setState(() {
      isLoading = true;
    });

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? token = prefs.getString('auth_token');

      if (token == null) {
        _showSnackBar('Error: You are not logged in.', isError: true);
        setState(() => isLoading = false);
        return;
      }

      final response = await http.get(
        Uri.parse('$baseUrl/api/leave-requests/my-requests'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        setState(() {
          pendingLeaves = List<Map<String, dynamic>>.from(data['pending']);
          approvedLeaves = List<Map<String, dynamic>>.from(data['approved']);
          rejectedLeaves = List<Map<String, dynamic>>.from(data['rejected']);

          pendingCount = pendingLeaves.length;
          approvedCount = approvedLeaves.length;
          rejectedCount = rejectedLeaves.length;

          leaveRequests = [
            ...pendingLeaves,
            ...approvedLeaves,
            ...rejectedLeaves,
          ];

          isLoading = false;
        });
      } else {
        throw Exception('Failed to load leaves');
      }
    } catch (e) {
      print('❌ Error: $e');
      _showSnackBar('Failed to load leave requests', isError: true);
      setState(() => isLoading = false);
    }
  }

  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.poppins(color: Colors.white),
        ),
        backgroundColor: isError ? Colors.red : AppColors.brightBlue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: EdgeInsets.all(16),
      ),
    );
  }

  List<dynamic> get filteredLeaves {
    if (selectedTab == 1) return approvedLeaves;
    if (selectedTab == 2) return rejectedLeaves;
    return leaveRequests;
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    // Set colors based on theme
    final backgroundColor = isDarkMode ? AppColors.darkBackground : AppColors.lightBackground;
    final cardColor = isDarkMode ? AppColors.darkCardBackground : AppColors.lightCardBackground;
    final textPrimaryColor = isDarkMode ? AppColors.darkTextPrimary : AppColors.lightTextPrimary;
    final textSecondaryColor = isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary;

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        backgroundColor: cardColor,
        elevation: 0,
        title: Row(
          children: [
            Icon(CupertinoIcons.doc_text_viewfinder, color: AppColors.brightBlue, size: 24),
            SizedBox(width: 12),
            Text(
              "Leave Requests",
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: textPrimaryColor,
              ),
            ),
          ],
        ),
        actions: [
          Container(
            margin: EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: AppColors.brightBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => ApplyLeaveScreen()),
                );
              },
              icon: Icon(CupertinoIcons.add, color: AppColors.brightBlue),
            ),
          ),
          Container(
            margin: EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: AppColors.lightGrayishBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (context) => FilterBottomSheet(
                    isDarkMode: isDarkMode,
                    cardColor: cardColor,
                    textPrimaryColor: textPrimaryColor,
                    textSecondaryColor: textSecondaryColor,
                  ),
                );
              },
              icon: Icon(CupertinoIcons.slider_horizontal_3, color: AppColors.lightGrayishBlue),
            ),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: RefreshIndicator(
            onRefresh: fetchLeaveRequests,
            color: AppColors.brightBlue,
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Statistics Cards
                  _buildStatsSection(isDarkMode, cardColor, textPrimaryColor, textSecondaryColor),

                  SizedBox(height: 24),

                  // Tab Section
                  _buildTabSection(isDarkMode, cardColor, textPrimaryColor),

                  SizedBox(height: 16),

                  // Leave List
                  Expanded(
                    child: isLoading
                        ? _buildLoadingState(isDarkMode, textSecondaryColor)
                        : filteredLeaves.isEmpty
                            ? _buildEmptyState(isDarkMode, textPrimaryColor, textSecondaryColor)
                            : _buildLeaveList(isDarkMode, cardColor, textPrimaryColor, textSecondaryColor),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatsSection(bool isDarkMode, Color cardColor, Color textPrimaryColor, Color textSecondaryColor) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.brightBlue, AppColors.similarBrightBlue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.brightBlue.withOpacity(0.3),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  CupertinoIcons.chart_bar_alt_fill,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              SizedBox(width: 12),
              Text(
                "Leave Summary",
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildStatCard("Pending", pendingCount.toString(), Colors.orange.shade300)),
              SizedBox(width: 12),
              Expanded(child: _buildStatCard("Approved", approvedCount.toString(), Colors.green.shade300)),
              SizedBox(width: 12),
              Expanded(child: _buildStatCard("Rejected", rejectedCount.toString(), Colors.red.shade300)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color accentColor) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabSection(bool isDarkMode, Color cardColor, Color textPrimaryColor) {
    return Container(
      padding: EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildTabButton("All", 0, isDarkMode, textPrimaryColor),
          _buildTabButton("Approved", 1, isDarkMode, textPrimaryColor),
          _buildTabButton("Rejected", 2, isDarkMode, textPrimaryColor),
        ],
      ),
    );
  }

  Widget _buildTabButton(String text, int index, bool isDarkMode, Color textColor) {
    final isSelected = selectedTab == index;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            selectedTab = index;
          });
        },
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          padding: EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.brightBlue : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              color: isSelected ? Colors.white : textColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState(bool isDarkMode, Color textSecondaryColor) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 50,
            height: 50,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.brightBlue),
              strokeWidth: 3,
            ),
          ),
          SizedBox(height: 24),
          Text(
            'Loading leave requests...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(bool isDarkMode, Color textPrimaryColor, Color textSecondaryColor) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            CupertinoIcons.doc_text,
            size: 60,
            color: AppColors.lightGrayishBlue,
          ),
          SizedBox(height: 16),
          Text(
            'No Leave Requests',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: textPrimaryColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'You haven\'t submitted any leave requests yet',
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaveList(bool isDarkMode, Color cardColor, Color textPrimaryColor, Color textSecondaryColor) {
    return ListView.builder(
      itemCount: filteredLeaves.length,
      itemBuilder: (context, index) {
        final item = filteredLeaves[index];
        return LeaveListCard(
          item: item,
          isDarkMode: isDarkMode,
          cardColor: cardColor,
          textPrimaryColor: textPrimaryColor,
          textSecondaryColor: textSecondaryColor,
        );
      },
    );
  }
}

// Leave List Card Widget
class LeaveListCard extends StatelessWidget {
  final Map<String, dynamic> item;
  final bool isDarkMode;
  final Color cardColor;
  final Color textPrimaryColor;
  final Color textSecondaryColor;

  const LeaveListCard({
    Key? key,
    required this.item,
    required this.isDarkMode,
    required this.cardColor,
    required this.textPrimaryColor,
    required this.textSecondaryColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final status = item['status'] ?? 'Pending';
    final startDate = item['startDate'] != null
        ? DateFormat('MMM dd, yyyy').format(DateTime.parse(item['startDate']))
        : 'N/A';
    final endDate = item['endDate'] != null
        ? DateFormat('MMM dd, yyyy').format(DateTime.parse(item['endDate']))
        : 'N/A';
    final days = item['startDate'] != null && item['endDate'] != null
        ? DateTime.parse(item['endDate']).difference(DateTime.parse(item['startDate'])).inDays + 1
        : 0;

    Color statusColor;
    Color statusBgColor;
    IconData statusIcon;

    switch (status.toLowerCase()) {
      case 'approved':
        statusColor = Colors.green.shade700;
        statusBgColor = Colors.green.shade50;
        statusIcon = CupertinoIcons.check_mark_circled_solid;
        break;
      case 'rejected':
        statusColor = Colors.red.shade700;
        statusBgColor = Colors.red.shade50;
        statusIcon = CupertinoIcons.xmark_circle_fill;
        break;
      default:
        statusColor = Colors.orange.shade700;
        statusBgColor = Colors.orange.shade50;
        statusIcon = CupertinoIcons.clock_solid;
    }

    if (isDarkMode) {
      statusBgColor = statusColor.withOpacity(0.2);
    }

    return Container(
      margin: EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: AppColors.lightGrayishBlue.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.brightBlue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      CupertinoIcons.calendar,
                      color: AppColors.brightBlue,
                      size: 16,
                    ),
                  ),
                  SizedBox(width: 12),
                  Text(
                    "Leave Request",
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: textPrimaryColor,
                    ),
                  ),
                ],
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: statusBgColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      statusIcon,
                      color: statusColor,
                      size: 14,
                    ),
                    SizedBox(width: 6),
                    Text(
                      status,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: statusColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 16),

          // Date Range
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isDarkMode
                ? AppColors.lightGrayishBlue.withOpacity(0.1)
                : AppColors.lightGrayishBlue.withOpacity(0.05),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  CupertinoIcons.calendar_today,
                  color: AppColors.brightBlue,
                  size: 20,
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Duration",
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: textSecondaryColor,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        "$startDate - $endDate",
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.brightBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    "$days ${days == 1 ? 'Day' : 'Days'}",
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.brightBlue,
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 16),

          // Details Row
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  "Leave Type",
                  item['type']?.toString().toUpperCase() ?? 'N/A',
                  CupertinoIcons.tag,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: _buildDetailItem(
                  "Balance",
                  item['leaveBalance']?.toString() ?? '0',
                  CupertinoIcons.chart_bar,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: _buildDetailItem(
                  "Approved By",
                  item['approvedBy'] ?? 'Pending',
                  CupertinoIcons.person,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: AppColors.lightGrayishBlue,
              size: 14,
            ),
            SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: textSecondaryColor,
              ),
            ),
          ],
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: textPrimaryColor,
          ),
        ),
      ],
    );
  }
}
