import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import './theme_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import '../widgets/bottom_navbar.dart';

// Define app colors
class AppColors {
  static const Color lightGrayishBlue = Color(0xFF9EB2BF);
  static const Color brightBlue = Color(0xFF209ACF);
  static const Color similarBrightBlue = Color(0xFF219CD0);
  static const Color darkGray = Color(0xFF272727);

  // Dark mode colors
  static const Color darkBackground = Color(0xFF272727);
  static const Color darkCardBackground = Color(0xFF333333);
  static const Color darkTextPrimary = Colors.white;
  static const Color darkTextSecondary = Color(0xFF9EB2BF);

  // Light mode colors
  static const Color lightBackground = Color(0xFFF5F7FA);
  static const Color lightCardBackground = Colors.white;
  static const Color lightTextPrimary = Color(0xFF272727);
  static const Color lightTextSecondary = Color(0xFF64748B);
}

class AnnouncementsScreen extends StatefulWidget {
  final int initialTabIndex;

  const AnnouncementsScreen({Key? key, this.initialTabIndex = 0}) : super(key: key);

  @override
  _AnnouncementsScreenState createState() => _AnnouncementsScreenState();
}

class _AnnouncementsScreenState extends State<AnnouncementsScreen> with SingleTickerProviderStateMixin {
  List<dynamic> announcements = [];
  final String baseUrl = 'https://attendance-system-production-4afd.up.railway.app';
  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';

  // Animation controller for staggered list
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 1000),
    );

    fetchAnnouncements();

    // Set initial tab index if provided
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.initialTabIndex != 0) {
        // Find the BottomNavBar in the widget tree
        final bottomNavBar = Navigator.of(context).widget.toString();
        print("Found bottom nav bar: $bottomNavBar");
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> fetchAnnouncements() async {
    setState(() {
      isLoading = true;
      hasError = false;
    });

    try {
      final response = await http.get(Uri.parse('$baseUrl/api/announcements/all'));

      if (response.statusCode == 200) {
        setState(() {
          announcements = json.decode(response.body);
          isLoading = false;

          // Start the animation after data is loaded
          _animationController.forward();
        });
      } else {
        setState(() {
          hasError = true;
          errorMessage = 'Server error: ${response.statusCode}';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        hasError = true;
        errorMessage = 'Network error: ${e.toString()}';
        isLoading = false;
      });
    }
  }

  String formatDate(String? isoDate) {
    if (isoDate == null) return 'No Date Available';
    try {
      final dateTime = DateTime.parse(isoDate);
      return DateFormat('hh:mm a, EEE, dd MMMM, yyyy').format(dateTime);
    } catch (e) {
      return 'Invalid Date';
    }
  }

  // Get a category color based on the title
  Color getCategoryColor(String? title) {
    if (title == null) return Color(0xFF9EB2BF); // Light grayish blue

    // Generate a consistent color based on the first character of the title
    final int seed = title.isNotEmpty ? title.codeUnitAt(0) : 0;
    final colors = [
      Color(0xFF209ACF), // Bright blue
      Color(0xFF219CD0), // Similar bright blue
      Color(0xFF9EB2BF), // Light grayish blue
    ];

    return colors[seed % colors.length];
  }

  // Get a category icon based on the title
  IconData getCategoryIcon(String? title) {
    if (title == null) return Icons.announcement;

    final String lowerTitle = title.toLowerCase();

    if (lowerTitle.contains('holiday') || lowerTitle.contains('vacation')) {
      return Icons.beach_access;
    } else if (lowerTitle.contains('meeting') || lowerTitle.contains('conference')) {
      return Icons.people;
    } else if (lowerTitle.contains('deadline') || lowerTitle.contains('due')) {
      return Icons.timer;
    } else if (lowerTitle.contains('update') || lowerTitle.contains('change')) {
      return Icons.update;
    } else if (lowerTitle.contains('event') || lowerTitle.contains('celebration')) {
      return Icons.event;
    }

    return Icons.campaign;
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    // Set colors based on theme
    final backgroundColor = isDarkMode ? AppColors.darkBackground : AppColors.lightBackground;
    final cardColor = isDarkMode ? AppColors.darkCardBackground : AppColors.lightCardBackground;
    final textPrimaryColor = isDarkMode ? AppColors.darkTextPrimary : AppColors.lightTextPrimary;
    final textSecondaryColor = isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary;

    // Start the animation
    _animationController.forward();

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        backgroundColor: cardColor,
        elevation: 0,
        title: Row(
          children: [
            Icon(
              CupertinoIcons.speaker_3_fill,
              color: AppColors.brightBlue,
              size: 24,
            ),
            SizedBox(width: 12),
            Text(
              'Announcements',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: textPrimaryColor,
              ),
            ),
          ],
        ),
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: textPrimaryColor,
            size: 20,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh_rounded,
              color: AppColors.brightBlue,
              size: 24,
            ),
            onPressed: fetchAnnouncements,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: fetchAnnouncements,
        color: AppColors.brightBlue,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: _buildContent(isDarkMode, textPrimaryColor, textSecondaryColor),
        ),
      ),
      bottomNavigationBar: BottomNavBar(),
    );
  }

  Widget _buildContent(bool isDarkMode, Color textPrimaryColor, Color textSecondaryColor) {
    if (isLoading) {
      return _buildLoadingState(isDarkMode);
    } else if (hasError) {
      return _buildErrorState(isDarkMode);
    } else if (announcements.isEmpty) {
      return _buildEmptyState(isDarkMode, textPrimaryColor, textSecondaryColor);
    } else {
      return _buildAnnouncementsList(isDarkMode);
    }
  }

  Widget _buildLoadingState(bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 50,
            height: 50,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.brightBlue),
              strokeWidth: 3,
            ),
          ),
          SizedBox(height: 24),
          Text(
            'Loading announcements...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(bool isDarkMode) {
    final errorColor = Color(0xFFE53E3E);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: 60,
            color: errorColor,
          ),
          SizedBox(height: 16),
          Text(
            'Oops! Something went wrong',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
            ),
          ),
          SizedBox(height: 8),
          Text(
            errorMessage,
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary,
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: fetchAnnouncements,
            icon: Icon(Icons.refresh),
            label: Text('Try Again'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.brightBlue,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              textStyle: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(bool isDarkMode, Color textPrimaryColor, Color textSecondaryColor) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.campaign_outlined,
            size: 60,
            color: AppColors.brightBlue,
          ),
          SizedBox(height: 16),
          Text(
            'No Announcements Yet',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: textPrimaryColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Check back later for updates',
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: textSecondaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnnouncementsList(bool isDarkMode) {
    return ListView.builder(
      physics: AlwaysScrollableScrollPhysics(),
      itemCount: announcements.length,
      itemBuilder: (context, index) {
        final announcement = announcements[index];
        final String? title = announcement['title'] as String?;
        final String? date = announcement['date'] as String?;
        final String? content = announcement['description'] as String?;

        // Create staggered animation for each item
        final Animation<double> animation = Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Interval(
              (1 / announcements.length) * index,
              (1 / announcements.length) * (index + 1),
              curve: Curves.easeOut,
            ),
          ),
        );

        return AnimatedBuilder(
          animation: animation,
          builder: (context, child) {
            return FadeTransition(
              opacity: animation,
              child: Transform.translate(
                offset: Offset(0, 50 * (1 - animation.value)),
                child: child,
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: _buildAnnouncementCard(title, formatDate(date), content, isDarkMode),
          ),
        );
      },
    );
  }

  Widget _buildAnnouncementCard(String? title, String? date, String? content, bool isDarkMode) {
    final Color categoryColor = getCategoryColor(title);
    final IconData categoryIcon = getCategoryIcon(title);

    // Set colors based on theme
    final cardColor = isDarkMode ? AppColors.darkCardBackground : AppColors.lightCardBackground;
    final textPrimaryColor = isDarkMode ? AppColors.darkTextPrimary : AppColors.lightTextPrimary;
    final textSecondaryColor = isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary;

    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              color: isDarkMode
                ? categoryColor.withOpacity(0.15)
                : categoryColor.withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isDarkMode
                      ? categoryColor.withOpacity(0.3)
                      : categoryColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    categoryIcon,
                    color: categoryColor,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title ?? 'No Title Available',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: textPrimaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  content ?? 'No Content Available',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: textPrimaryColor.withOpacity(0.9),
                    height: 1.5,
                  ),
                ),
                SizedBox(height: 16),

                // Date with icon
                Row(
                  children: [
                    Icon(
                      Icons.access_time_rounded,
                      size: 16,
                      color: textSecondaryColor,
                    ),
                    SizedBox(width: 6),
                    Text(
                      date ?? 'No Date Available',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: textSecondaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
