# Exercise 1: Function to find names that are not in both lists

def unique_names(names1, names2):
    return list(set(names1) ^ set(names2)) 

# Test case
Names1 = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"]
Names2 = ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"]
exercise1_result = unique_names(Names1, Names2)
exercise1_result


# Exercise 2: Function to calculate the mean manually

def calculate_mean(numbers):
    total_sum = sum(numbers)
    count = len(numbers)
    return total_sum / count 
    if count > 0 else 0

# Test case
numbers = [51, 13, 27, 81, 64, 19, 32, 26, 42, 89]
exercise2_result = calculate_mean(numbers)
exercise2_result


# Exercise 3: Variance calculation using nested functions

def var_n(numbers):

    mean_lambda = lambda nums: sum(nums) / len(nums) if len(nums) > 0 else 0
    
    mean_value = mean_lambda(numbers)
   variance = sum((x - mean_value) ** 2 for x in numbers) / (len(numbers) - 1)
    
    return variance

# Test case using numbers from Exercise 2
numbers = [51, 13, 27, 81, 64, 19, 32, 26, 42, 89]
variance_result = var_n(numbers)

# Checking if mean_lambda gives the same result as the function from Exercise 2
mean_lambda_result = (lambda nums: sum(nums) / len(nums))(numbers)
mean_function_result = calculate_mean(numbers)

variance_result, mean_lambda_result, mean_function_result



# Exercise 4: Function to return the next month


months = ["January", "February", "March", "April", "May", "June", 
          "July", "August", "September", "October", "November", "December"]

def next_month(current_month="January"):
 
    try:
        # Normalize input (case-insensitive matching)
        current_month = current_month.capitalize()
        
        # Find index of current month
        index = months.index(current_month)
        
        # Return the next month (looping back to January after December)
        return months[(index + 1) % len(months)]
    
    except ValueError:
        return "Error: Invalid month name. Please check spelling."

# Test cases
print(next_month("June"))       # Expected output: "July"
print(next_month("December"))   # Expected output: "January"
print(next_month())             # Expected output: "February" (default is "January")
print(next_month("abc"))        # Expected output: "Error: Invalid month name."


# Exercise 5: Function to count the most common words in a text file
from collections import Counter

def count_most_common_words(filename, top_n=10):
  
    try:
        with open(filename, "r", encoding="utf-8") as file:
            text = file.read().lower()  # Convert text to lowercase for uniformity
            
        words = text.split()  # Split text into words
        word_counts = Counter(words)  # Count occurrences
        
        # Get the top_n most common words
        most_common = word_counts.most_common(top_n)
        
        print("Top", top_n, "most common words:")
        for word, count in most_common:
            print(f"{word}: {count}")
    
    except FileNotFoundError:
        print(f"Error: The file {filename} was not found.")
    except Exception as e:
        print(f"An error occurred: {e}")

# Test the function
count_most_common_words("Shiinaha.txt")


#6. Exercise 6: Card shuffling and drawing
import random

# Define the Card class
class Card:
    def __init__(self, suit, rank):
        self.suit = suit
        self.rank = rank

    def __str__(self):
        return f"{self.rank} of {self.suit}"

# Define suits and ranks
suits = ["Spades", "Clubs", "Hearts", "Diamonds"]
ranks = ["2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A"]

# Create a deck of cards
deck = [Card(suit, rank) for suit in suits for rank in ranks]

# Shuffle the deck
random.shuffle(deck)

# Draw a card from the shuffled deck
drawn_card = deck.pop()

print("Drawn Card:", drawn_card)

# Shuffle again and draw another card
random.shuffle(deck)
drawn_card_again = deck.pop()
print("Drawn Card After Reshuffling:", drawn_card_again)
