import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'theme_provider.dart';

class QRCodeScreen extends StatelessWidget {
  const QRCodeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return Scaffold(
      backgroundColor: isDarkMode ? Color(0xFF272727) : Colors.white,
      appBar: AppBar(
        title: Text(
          'QR Code Scanner',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Color(0xFF272727),
          ),
        ),
        backgroundColor: isDarkMode ? Color(0xFF272727) : Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.qr_code_scanner_rounded,
              size: 100,
              color: Color(0xFF209ACF),
            ),
            SizedBox(height: 24),
            Text(
              'QR Code Scanner',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Color(0xFF272727),
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Scan QR codes for attendance',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Color(0xFF9EB2BF),
              ),
            ),
            SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                // TODO: Implement QR scanner
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('QR Scanner coming soon!'),
                    backgroundColor: Color(0xFF209ACF),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF209ACF),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Start Scanning',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
