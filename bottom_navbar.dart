import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../screens/home.dart';
import '../screens/attendance.dart';
import '../screens/qr_code.dart';
import '../screens/leave_request.dart';
import '../screens/profile_screen.dart';
import '../screens/CheckInSuccessScreen.dart';
import './theme_provider.dart';

class BottomNavBar extends StatefulWidget {
  @override
  _BottomNavBarState createState() => _BottomNavBarState();
}

class _BottomNavBarState extends State<BottomNavBar> with SingleTickerProviderStateMixin {
  int _currentIndex = 0;
  DateTime? checkInTime;
  bool isLoading = true;

  // Animation controller for tab transitions
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Page controller for smooth page transitions
  late PageController _pageController;

  // Tab items data with your color palette
  final List<Map<String, dynamic>> _tabItems = [
    {
      'icon': Icons.home_rounded,
      'activeIcon': Icons.home_rounded,
      'label': 'Home',
      'color': Color(0xFF209ACF), // Bright blue
    },
    {
      'icon': Icons.access_time_rounded,
      'activeIcon': Icons.access_time_filled_rounded,
      'label': 'Attendance',
      'color': Color(0xFF219CD0), // Similar bright blue
    },
    {
      'icon': Icons.qr_code_scanner_rounded,
      'activeIcon': Icons.qr_code_scanner_rounded,
      'label': 'QR Code',
      'color': Color(0xFF209ACF), // Bright blue
    },
    {
      'icon': Icons.event_note_rounded,
      'activeIcon': Icons.event_available_rounded,
      'label': 'Leave',
      'color': Color(0xFF219CD0), // Similar bright blue
    },
    {
      'icon': Icons.person_outline_rounded,
      'activeIcon': Icons.person_rounded,
      'label': 'Profile',
      'color': Color(0xFF209ACF), // Bright blue
    },
  ];

  late List<Widget> _screens;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // Initialize page controller
    _pageController = PageController(initialPage: _currentIndex);

    // Load check-in time
    loadCheckInTime();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> loadCheckInTime() async {
    print('🔍 Loading checkInTime from SharedPreferences...');
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final storedTime = prefs.getString('checkInTime');

    if (storedTime != null) {
      try {
        checkInTime = DateTime.parse(storedTime);
        print('✅ Loaded checkInTime: $checkInTime');
      } catch (e) {
        print('❌ Failed to parse checkInTime: $e');
      }
    } else {
      print('❌ No checkInTime in SharedPreferences');
    }

    // Initialize screens after checkInTime is loaded
    _screens = [
      HomeAttendanceScreen(),
      AttendanceDetailsScreen(),
      checkInTime != null
          ? CheckInSuccessScreen(checkInTime: checkInTime!)
          : const QRCodeScanner(),
      LeaveScreen(),
      ProfileScreen(),
    ];

    setState(() {
      isLoading = false;
      _animationController.forward();
    });
  }

  void _onTabTapped(int index) {
    if (index == _currentIndex) return;

    // Animate to the new page
    _pageController.animateToPage(
      index,
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );

    setState(() => _currentIndex = index);
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    if (isLoading) {
      return _buildLoadingScreen(isDarkMode);
    }

    print('📱 Building UI - checkInTime = $checkInTime');

    return Scaffold(
      body: PageView(
        controller: _pageController,
        physics: NeverScrollableScrollPhysics(), // Disable swiping
        children: _screens,
        onPageChanged: (index) {
          setState(() => _currentIndex = index);
        },
      ),
      bottomNavigationBar: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          decoration: BoxDecoration(
            color: isDarkMode ? Color(0xFF272727) : Colors.white, // Dark gray for dark mode
            boxShadow: [
              BoxShadow(
                color: Color(0xFF9EB2BF).withOpacity(isDarkMode ? 0.2 : 0.1), // Light grayish blue for shadow
                blurRadius: 15,
                offset: Offset(0, -3),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: List.generate(_tabItems.length, (index) {
                  final bool isSelected = index == _currentIndex;
                  final Map<String, dynamic> item = _tabItems[index];

                  return _buildNavItem(
                    icon: isSelected ? item['activeIcon'] : item['icon'],
                    label: item['label'],
                    color: item['color'],
                    isSelected: isSelected,
                    isDarkMode: isDarkMode,
                    onTap: () => _onTabTapped(index),
                  );
                }),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingScreen(bool isDarkMode) {
    return Scaffold(
      backgroundColor: isDarkMode ? Color(0xFF272727) : Colors.white, // Dark gray for dark mode
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 50,
              height: 50,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF209ACF)), // Bright blue
                strokeWidth: 3,
              ),
            ),
            SizedBox(height: 24),
            Text(
              'Loading...',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: isDarkMode ? Color(0xFF9EB2BF) : Color(0xFF272727), // Light grayish blue for dark mode, dark gray for light mode
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
    bool isDarkMode = false,
  }) {
    return InkWell(
      onTap: onTap,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(isDarkMode ? 0.2 : 0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? color : (isDarkMode ? Color(0xFF9EB2BF) : Color(0xFF9EB2BF).withOpacity(0.7)), // Light grayish blue
              size: 24,
            ),
            SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? color : (isDarkMode ? Color(0xFF9EB2BF) : Color(0xFF9EB2BF).withOpacity(0.7)), // Light grayish blue
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Method to programmatically navigate to a specific tab
  void navigateToTab(int index) {
    if (index >= 0 && index < _tabItems.length) {
      _onTabTapped(index);
    }
  }
}
