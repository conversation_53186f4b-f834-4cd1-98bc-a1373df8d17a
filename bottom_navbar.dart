import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';
import '../screens/home.dart';
import '../screens/attendance.dart';
import '../screens/qr_code.dart';
import '../screens/leave_request.dart';
import '../screens/profile_screen.dart';
import '../screens/CheckInSuccessScreen.dart';

class BottomNavBar extends StatefulWidget {
  @override
  _BottomNavBarState createState() => _BottomNavBarState();
}

class _BottomNavBarState extends State<BottomNavBar> with SingleTickerProviderStateMixin {
  int _currentIndex = 0;
  DateTime? checkInTime;
  bool isLoading = true;
  
  // Animation controller for tab transitions
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Page controller for smooth page transitions
  late PageController _pageController;

  // Tab items data
  final List<Map<String, dynamic>> _tabItems = [
    {
      'icon': Icons.home_rounded,
      'activeIcon': Icons.home_rounded,
      'label': 'Home',
      'color': Color(0xFF4A90E2),
    },
    {
      'icon': Icons.access_time_rounded,
      'activeIcon': Icons.access_time_filled_rounded,
      'label': 'Attendance',
      'color': Color(0xFF50E3C2),
    },
    {
      'icon': Icons.qr_code_scanner_rounded,
      'activeIcon': Icons.qr_code_scanner_rounded,
      'label': 'QR Code',
      'color': Color(0xFF9B59B6),
    },
    {
      'icon': Icons.event_note_rounded,
      'activeIcon': Icons.event_available_rounded,
      'label': 'Leave',
      'color': Color(0xFFE6704A),
    },
    {
      'icon': Icons.person_outline_rounded,
      'activeIcon': Icons.person_rounded,
      'label': 'Profile',
      'color': Color(0xFF1ABC9C),
    },
  ];

  late List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    // Initialize page controller
    _pageController = PageController(initialPage: _currentIndex);
    
    // Load check-in time
    loadCheckInTime();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> loadCheckInTime() async {
    print('🔍 Loading checkInTime from SharedPreferences...');
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final storedTime = prefs.getString('checkInTime');

    if (storedTime != null) {
      try {
        checkInTime = DateTime.parse(storedTime);
        print('✅ Loaded checkInTime: $checkInTime');
      } catch (e) {
        print('❌ Failed to parse checkInTime: $e');
      }
    } else {
      print('❌ No checkInTime in SharedPreferences');
    }

    // Initialize screens after checkInTime is loaded
    _screens = [
      HomeAttendanceScreen(),
      AttendanceDetailsScreen(),
      checkInTime != null
          ? CheckInSuccessScreen(checkInTime: checkInTime!)
          : const QRCodeScanner(),
      LeaveScreen(),
      ProfileScreen(),
    ];

    setState(() {
      isLoading = false;
      _animationController.forward();
    });
  }

  void _onTabTapped(int index) {
    if (index == _currentIndex) return;
    
    // Animate to the new page
    _pageController.animateToPage(
      index,
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    
    setState(() => _currentIndex = index);
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingScreen();
    }

    print('📱 Building UI - checkInTime = $checkInTime');

    return Scaffold(
      body: PageView(
        controller: _pageController,
        physics: NeverScrollableScrollPhysics(), // Disable swiping
        children: _screens,
        onPageChanged: (index) {
          setState(() => _currentIndex = index);
        },
      ),
      bottomNavigationBar: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.06),
                blurRadius: 15,
                offset: Offset(0, -3),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: List.generate(_tabItems.length, (index) {
                  final bool isSelected = index == _currentIndex;
                  final Map<String, dynamic> item = _tabItems[index];
                  
                  return _buildNavItem(
                    icon: isSelected ? item['activeIcon'] : item['icon'],
                    label: item['label'],
                    color: item['color'],
                    isSelected: isSelected,
                    onTap: () => _onTabTapped(index),
                  );
                }),
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 50,
              height: 50,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4A90E2)),
                strokeWidth: 3,
              ),
            ),
            SizedBox(height: 24),
            Text(
              'Loading...',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Color(0xFF64748B),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? color : Color(0xFF94A3B8),
              size: 24,
            ),
            SizedBox(height: 4),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? color : Color(0xFF94A3B8),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Method to programmatically navigate to a specific tab
  void navigateToTab(int index) {
    if (index >= 0 && index < _tabItems.length) {
      _onTabTapped(index);
    }
  }
}
