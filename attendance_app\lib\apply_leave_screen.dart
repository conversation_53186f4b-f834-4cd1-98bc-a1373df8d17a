import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter/cupertino.dart';

import './theme_provider.dart';
import 'bottom_navbar.dart';

// Define app colors
class AppColors {
  static const Color lightGrayishBlue = Color(0xFF9EB2BF);
  static const Color brightBlue = Color(0xFF209ACF);
  static const Color similarBrightBlue = Color(0xFF219CD0);
  static const Color darkGray = Color(0xFF272727);

  // Dark mode colors
  static const Color darkBackground = Color(0xFF272727);
  static const Color darkCardBackground = Color(0xFF333333);
  static const Color darkTextPrimary = Colors.white;
  static const Color darkTextSecondary = Color(0xFF9EB2BF);

  // Light mode colors
  static const Color lightBackground = Color(0xFFF5F7FA);
  static const Color lightCardBackground = Colors.white;
  static const Color lightTextPrimary = Color(0xFF272727);
  static const Color lightTextSecondary = Color(0xFF64748B);
}

class ApplyLeaveScreen extends StatefulWidget {
  @override
  _ApplyLeaveScreenState createState() => _ApplyLeaveScreenState();
}

class _ApplyLeaveScreenState extends State<ApplyLeaveScreen> with TickerProviderStateMixin {
  TextEditingController reasonController = TextEditingController(text: "I need to take a medical leave.");
  String selectedLeaveType = "medical";
  DateTime? startDate = DateTime.now();
  DateTime? endDate = DateTime.now().add(Duration(days: 1));
  bool isSubmitting = false;

  List<Map<String, dynamic>> leaveTypes = [
    {"value": "casual", "label": "Casual Leave", "icon": Icons.beach_access},
    {"value": "medical", "label": "Medical Leave", "icon": Icons.local_hospital},
    {"value": "other", "label": "Other", "icon": Icons.more_horiz},
  ];

  final String baseUrl = 'https://attendance-system-production-4afd.up.railway.app';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(begin: Offset(0, 0.3), end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    reasonController.dispose();
    super.dispose();
  }

  Future<void> selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? startDate! : endDate!,
      firstDate: DateTime(2022),
      lastDate: DateTime(2030),
      builder: (context, child) {
        final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
        final isDarkMode = themeProvider.isDarkMode;

        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.brightBlue,
              onPrimary: Colors.white,
              surface: isDarkMode ? AppColors.darkCardBackground : AppColors.lightCardBackground,
              onSurface: isDarkMode ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          startDate = picked;
        } else {
          endDate = picked;
        }
      });
    }
  }

  Future<void> submitLeave() async {
    if (reasonController.text.trim().isEmpty) {
      _showSnackBar("Please provide a reason for leave", isError: true);
      return;
    }

    setState(() {
      isSubmitting = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        _showSnackBar("You are not logged in.", isError: true);
        setState(() {
          isSubmitting = false;
        });
        return;
      }

      final Map<String, dynamic> leaveData = {
        "type": selectedLeaveType,
        "startDate": startDate?.toIso8601String(),
        "endDate": endDate?.toIso8601String(),
        "description": reasonController.text.trim(),
      };

      final response = await http.post(
        Uri.parse("$baseUrl/api/leave-requests/submit"),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $token",
        },
        body: json.encode(leaveData),
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        _showSnackBar("Leave request submitted successfully!", isError: false);
        // Clear form
        reasonController.clear();
        setState(() {
          selectedLeaveType = "medical";
          startDate = DateTime.now();
          endDate = DateTime.now().add(Duration(days: 1));
        });
      } else {
        _showSnackBar("Failed to submit leave request.", isError: true);
      }
    } catch (e) {
      _showSnackBar("Network error. Please try again.", isError: true);
    } finally {
      setState(() {
        isSubmitting = false;
      });
    }
  }

  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.poppins(color: Colors.white),
        ),
        backgroundColor: isError ? Colors.red : AppColors.brightBlue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: EdgeInsets.all(16),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    // Set colors based on theme
    final backgroundColor = isDarkMode ? AppColors.darkBackground : AppColors.lightBackground;
    final cardColor = isDarkMode ? AppColors.darkCardBackground : AppColors.lightCardBackground;
    final textPrimaryColor = isDarkMode ? AppColors.darkTextPrimary : AppColors.lightTextPrimary;
    final textSecondaryColor = isDarkMode ? AppColors.darkTextSecondary : AppColors.lightTextSecondary;

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        backgroundColor: cardColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios_new, color: textPrimaryColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Row(
          children: [
            Icon(CupertinoIcons.doc_text, color: AppColors.brightBlue, size: 24),
            SizedBox(width: 12),
            Text(
              "Apply Leave",
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: textPrimaryColor,
              ),
            ),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Card
                Container(
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.brightBlue, AppColors.similarBrightBlue],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.brightBlue.withOpacity(0.3),
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          CupertinoIcons.calendar_badge_plus,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Request Leave",
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            Text(
                              "Fill out the form below to submit your leave request",
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.white.withOpacity(0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24),

                // Leave Type Selection
                _buildSectionTitle("Leave Type", textPrimaryColor),
                SizedBox(height: 12),
                _buildLeaveTypeSelector(isDarkMode, cardColor, textPrimaryColor),

                SizedBox(height: 24),

                // Date Selection
                _buildSectionTitle("Duration", textPrimaryColor),
                SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildDatePicker(
                        "Start Date",
                        startDate!,
                        () => selectDate(context, true),
                        isDarkMode,
                        cardColor,
                        textPrimaryColor,
                        textSecondaryColor,
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: _buildDatePicker(
                        "End Date",
                        endDate!,
                        () => selectDate(context, false),
                        isDarkMode,
                        cardColor,
                        textPrimaryColor,
                        textSecondaryColor,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 24),

                // Reason Input
                _buildSectionTitle("Reason", textPrimaryColor),
                SizedBox(height: 12),
                _buildReasonInput(isDarkMode, cardColor, textPrimaryColor, textSecondaryColor),

                SizedBox(height: 32),

                // Submit Button
                _buildSubmitButton(isDarkMode),

                SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: BottomNavBar(),
    );
  }

  Widget _buildSectionTitle(String title, Color textColor) {
    return Text(
      title,
      style: GoogleFonts.poppins(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: textColor,
      ),
    );
  }

  Widget _buildLeaveTypeSelector(bool isDarkMode, Color cardColor, Color textColor) {
    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: leaveTypes.map((type) {
          final isSelected = selectedLeaveType == type["value"];
          return GestureDetector(
            onTap: () {
              setState(() {
                selectedLeaveType = type["value"];
              });
            },
            child: Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isSelected
                  ? AppColors.brightBlue.withOpacity(0.1)
                  : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                border: isSelected
                  ? Border.all(color: AppColors.brightBlue, width: 2)
                  : null,
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isSelected
                        ? AppColors.brightBlue
                        : AppColors.lightGrayishBlue.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      type["icon"],
                      color: isSelected ? Colors.white : AppColors.lightGrayishBlue,
                      size: 20,
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      type["label"],
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected ? AppColors.brightBlue : textColor,
                      ),
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: AppColors.brightBlue,
                      size: 24,
                    ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDatePicker(
    String label,
    DateTime date,
    VoidCallback onTap,
    bool isDarkMode,
    Color cardColor,
    Color textPrimaryColor,
    Color textSecondaryColor,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.lightGrayishBlue.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
              blurRadius: 10,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: textSecondaryColor,
              ),
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  CupertinoIcons.calendar,
                  color: AppColors.brightBlue,
                  size: 20,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    DateFormat('MMM dd, yyyy').format(date),
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: textPrimaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReasonInput(
    bool isDarkMode,
    Color cardColor,
    Color textPrimaryColor,
    Color textSecondaryColor,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(isDarkMode ? 0.2 : 0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: reasonController,
        maxLines: 4,
        style: GoogleFonts.poppins(
          fontSize: 16,
          color: textPrimaryColor,
        ),
        decoration: InputDecoration(
          hintText: "Please provide a detailed reason for your leave request...",
          hintStyle: GoogleFonts.poppins(
            fontSize: 14,
            color: textSecondaryColor,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: AppColors.lightGrayishBlue.withOpacity(0.3),
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: AppColors.lightGrayishBlue.withOpacity(0.3),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: AppColors.brightBlue,
              width: 2,
            ),
          ),
          contentPadding: EdgeInsets.all(16),
          filled: true,
          fillColor: cardColor,
        ),
      ),
    );
  }

  Widget _buildSubmitButton(bool isDarkMode) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.brightBlue.withOpacity(0.3),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: isSubmitting ? null : submitLeave,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.brightBlue,
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: EdgeInsets.zero,
        ),
        child: isSubmitting
          ? Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    strokeWidth: 2,
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  "Submitting...",
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  CupertinoIcons.paperplane,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 12),
                Text(
                  "Submit Leave Request",
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
      ),
    );
  }
}
