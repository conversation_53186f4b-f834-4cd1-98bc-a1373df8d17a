import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import './theme_provider.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutDeveloperScreen extends StatelessWidget {
  const AboutDeveloperScreen({Key? key}) : super(key: key);

  // Function to launch URLs
  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  // Function to make phone calls
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri uri = Uri(scheme: 'tel', path: phoneNumber);
    if (!await launchUrl(uri)) {
      throw Exception('Could not launch $uri');
    }
  }

  // Function to open WhatsApp
  Future<void> _openWhatsApp(String phoneNumber) async {
    final Uri uri = Uri.parse('https://wa.me/$phoneNumber');
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch WhatsApp');
    }
  }

  // Function to open Instagram
  Future<void> _openInstagram(String username) async {
    final Uri uri = Uri.parse('https://instagram.com/$username');
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch Instagram');
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;
    
    // Define colors based on theme
    final backgroundColor = isDarkMode ? Color(0xFF1A202C) : Color(0xFFF8FAFC);
    final cardColor = isDarkMode ? Color(0xFF2D3748) : Colors.white;
    final primaryTextColor = isDarkMode ? Colors.white : Color(0xFF2D3748);
    final secondaryTextColor = isDarkMode ? Color(0xFFE2E8F0) : Color(0xFF64748B);
    final accentColor = Color(0xFF4A90E2);
    
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        backgroundColor: cardColor,
        elevation: 0,
        title: Text(
          "About Developer",
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: primaryTextColor,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_new,
            color: primaryTextColor,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header section with logo and company name
            Container(
              padding: EdgeInsets.symmetric(vertical: 40),
              decoration: BoxDecoration(
                color: cardColor,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Logo with animated container
                  Container(
                    padding: EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          accentColor,
                          Color(0xFF50E3C2),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                    ),
                    child: CircleAvatar(
                      radius: 60,
                      backgroundColor: cardColor,
                      child: CircleAvatar(
                        radius: 55,
                        backgroundImage: AssetImage('assets/dev_logo.png'),
                      ),
                    ),
                  ),
                  SizedBox(height: 20),
                  
                  // Company name with gradient text
                  ShaderMask(
                    shaderCallback: (bounds) {
                      return LinearGradient(
                        colors: [
                          accentColor,
                          Color(0xFF50E3C2),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ).createShader(bounds);
                    },
                    child: Text(
                      'Recent IT',
                      style: GoogleFonts.poppins(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  SizedBox(height: 8),
                  
                  // Subtitle
                  Text(
                    'Developer of Attendo',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      color: secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            
            // About section
            Container(
              margin: EdgeInsets.all(20),
              padding: EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: cardColor,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Section title
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline_rounded,
                        color: accentColor,
                        size: 24,
                      ),
                      SizedBox(width: 12),
                      Text(
                        'About Us',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  
                  // About text
                  Text(
                    'Attendo is a smart attendance management system produced by Recent IT, a technology company founded in November 2023.',
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      height: 1.6,
                      color: primaryTextColor,
                    ),
                    textAlign: TextAlign.justify,
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Attendo simplifies daily attendance tracking with seamless check-ins, leave management, and insightful analytics.',
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      height: 1.6,
                      color: primaryTextColor,
                    ),
                    textAlign: TextAlign.justify,
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Recent IT is committed to delivering high-quality digital solutions that empower organizations in Somalia and beyond. The app is designed with a focus on user experience, security, and efficiency — helping teams stay organized and productive every day.',
                    style: GoogleFonts.poppins(
                      fontSize: 15,
                      height: 1.6,
                      color: primaryTextColor,
                    ),
                    textAlign: TextAlign.justify,
                  ),
                ],
              ),
            ),
            
            // Contact section
            Container(
              margin: EdgeInsets.only(left: 20, right: 20, bottom: 30),
              padding: EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: cardColor,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Section title
                  Row(
                    children: [
                      Icon(
                        Icons.contact_phone_rounded,
                        color: accentColor,
                        size: 24,
                      ),
                      SizedBox(width: 12),
                      Text(
                        'Connect With Us',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: primaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  
                  // Social media buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildSocialButton(
                        icon: Icons.call_rounded,
                        color: Color(0xFFE53E3E),
                        label: 'Call',
                        isDarkMode: isDarkMode,
                        onTap: () => _makePhoneCall('+252615301507'),
                      ),
                      _buildSocialButton(
                        icon: Icons.language_rounded,
                        color: Color(0xFF805AD5),
                        label: 'Website',
                        isDarkMode: isDarkMode,
                        onTap: () => _launchUrl('https://recentit.so'),
                      ),
                      _buildSocialButton(
                        icon: FontAwesomeIcons.whatsapp,
                        color: Color(0xFF38A169),
                        label: 'WhatsApp',
                        isDarkMode: isDarkMode,
                        onTap: () => _openWhatsApp('252615301507'),
                      ),
                      _buildSocialButton(
                        icon: FontAwesomeIcons.instagram,
                        color: Color(0xFFED64A6),
                        label: 'Instagram',
                        isDarkMode: isDarkMode,
                        onTap: () => _openInstagram('recentit.so'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Footer with version
            Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: Text(
                '© 2023 Recent IT • v1.0.0',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: secondaryTextColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSocialButton({
    required IconData icon,
    required Color color,
    required String label,
    required bool isDarkMode,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(isDarkMode ? 0.2 : 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: isDarkMode ? Colors.white70 : Color(0xFF64748B),
            ),
          ),
        ],
      ),
    );
  }
}
