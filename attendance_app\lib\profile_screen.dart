import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:google_fonts/google_fonts.dart';
import 'about_developers_page.dart';
import 'my_profile_screen.dart';
import './theme_provider.dart';
import 'terms_and_conditions_page.dart';
import 'package:flutter/cupertino.dart';

class ProfileScreen extends StatefulWidget {
  @override
  _ProfileScreenState createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> with SingleTickerProviderStateMixin {
  String? username;
  String? jobTitle;
  String? imageUrl;
  bool isLoading = true;
  bool hasError = false;
  
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final String baseUrl = 'https://attendance-system-production-4afd.up.railway.app';

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 800),
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    
    fetchEmployeeProfile();
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> fetchEmployeeProfile() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        print("❌ No token found");
        setState(() {
          hasError = true;
          isLoading = false;
        });
        return;
      }

      final response = await http.get(
        Uri.parse('$baseUrl/api/employees/me'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print("🛰️ Status Code: ${response.statusCode}");
      print("📦 Raw Body: ${response.body}");

      if (response.statusCode == 200) {
        final userData = jsonDecode(response.body);
        print("✅ Username: ${userData['username']}");
        print("✅ JobTitle: ${userData['jobTitle']}");

        setState(() {
          username = userData['username'];
          jobTitle = userData['jobTitle'];
          imageUrl = '$baseUrl/${userData['image']}';
          isLoading = false;
          
          // Start animation after data is loaded
          _animationController.forward();
        });
      } else {
        print("❌ Failed to fetch profile");
        setState(() {
          hasError = true;
          isLoading = false;
        });
      }
    } catch (e) {
      print("❌ Error: $e");
      setState(() {
        hasError = true;
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return Scaffold(
      backgroundColor: isDarkMode ? Color(0xFF1A202C) : Color(0xFFF8FAFC),
      appBar: AppBar(
        backgroundColor: isDarkMode ? Color(0xFF2D3748) : Colors.white,
        elevation: 0,
        title: Text(
          "Profile",
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Color(0xFF2D3748),
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(
              Icons.settings_outlined,
              color: isDarkMode ? Colors.white70 : Color(0xFF64748B),
            ),
            onPressed: () {
              // Navigate to settings
            },
          ),
        ],
      ),
      body: isLoading 
          ? _buildLoadingState()
          : hasError 
              ? _buildErrorState()
              : _buildProfileContent(context, isDarkMode),
    );
  }
  
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 50,
            height: 50,
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4A90E2)),
              strokeWidth: 3,
            ),
          ),
          SizedBox(height: 24),
          Text(
            'Loading profile...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: Color(0xFF64748B),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: 60,
            color: Color(0xFFE53E3E),
          ),
          SizedBox(height: 16),
          Text(
            'Failed to load profile',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2D3748),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Please check your connection and try again',
            textAlign: TextAlign.center,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Color(0xFF64748B),
            ),
          ),
          SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: fetchEmployeeProfile,
            icon: Icon(Icons.refresh),
            label: Text('Try Again'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF4A90E2),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              textStyle: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildProfileContent(BuildContext context, bool isDarkMode) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              SizedBox(height: 20),
              
              // Profile Header
              _buildProfileHeader(isDarkMode),
              
              SizedBox(height: 30),
              
              // Profile Options
              _buildProfileOptions(context, isDarkMode),
              
              SizedBox(height: 30),
              
              // Logout Button
              _buildLogoutButton(isDarkMode),
              
              SizedBox(height: 30),
              
              // App Version
              Text(
                "Version 1.0.0",
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: isDarkMode ? Colors.white60 : Color(0xFF94A3B8),
                ),
              ),
              
              SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildProfileHeader(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDarkMode ? Color(0xFF2D3748) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Stack(
            children: [
              Container(
                padding: EdgeInsets.all(4),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Color(0xFF4A90E2),
                    width: 2,
                  ),
                ),
                child: CircleAvatar(
                  radius: 50,
                  backgroundColor: Color(0xFFE2E8F0),
                  backgroundImage: imageUrl != null
                      ? NetworkImage(imageUrl!)
                      : AssetImage('assets/hasina.jpg') as ImageProvider,
                ),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Color(0xFF4A90E2),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Color(0xFF4A90E2).withOpacity(0.3),
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    CupertinoIcons.camera_fill,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Text(
            username ?? "Loading...",
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Color(0xFF2D3748),
            ),
          ),
          SizedBox(height: 4),
          Text(
            jobTitle ?? "Loading...",
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: isDarkMode ? Colors.white70 : Color(0xFF64748B),
            ),
          ),
          SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => ProfileScreen()),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF4A90E2),
                foregroundColor: Colors.white,
                elevation: 0,
                padding: EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                "Edit Profile",
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildProfileOptions(BuildContext context, bool isDarkMode) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? Color(0xFF2D3748) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          ProfileOption(
            icon: CupertinoIcons.person,
            title: "My Profile",
            isDarkMode: isDarkMode,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => ProfileScreen()),
              );
            },
          ),
          _buildDivider(isDarkMode),
          SwitchListTile(
            value: themeProvider.isDarkMode,
            onChanged: (value) {
              themeProvider.toggleTheme(value);
            },
            title: Row(
              children: [
                Icon(
                  CupertinoIcons.moon_stars_fill,
                  color: isDarkMode ? Color(0xFF4A90E2) : Color(0xFF64748B),
                  size: 22,
                ),
                SizedBox(width: 12),
                Text(
                  "Dark Mode",
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode ? Colors.white : Color(0xFF2D3748),
                  ),
                ),
              ],
            ),
            activeColor: Color(0xFF4A90E2),
            inactiveTrackColor: isDarkMode ? Color(0xFF4A5568) : Color(0xFFE2E8F0),
          ),
          _buildDivider(isDarkMode),
          ProfileOption(
            icon: CupertinoIcons.doc_text,
            title: "Terms & Conditions",
            isDarkMode: isDarkMode,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => TermsAndConditionsPage()),
              );
            },
          ),
          _buildDivider(isDarkMode),
          ProfileOption(
            icon: CupertinoIcons.info,
            title: "About Developers",
            isDarkMode: isDarkMode,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => AboutDevelopersPage()),
              );
            },
          ),
        ],
      ),
    );
  }
  
  Widget _buildDivider(bool isDarkMode) {
    return Divider(
      height: 1,
      thickness: 1,
      indent: 56,
      endIndent: 0,
      color: isDarkMode ? Color(0xFF4A5568).withOpacity(0.3) : Color(0xFFE2E8F0),
    );
  }
  
  Widget _buildLogoutButton(bool isDarkMode) {
    return Container(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () async {
          // Clear shared preferences and navigate to login
          final prefs = await SharedPreferences.getInstance();
          await prefs.clear();
          Navigator.of(context).pushReplacementNamed('/login');
        },
        icon: Icon(Icons.logout_rounded),
        label: Text("Logout"),
        style: ElevatedButton.styleFrom(
          backgroundColor: isDarkMode ? Color(0xFF2D3748) : Colors.white,
          foregroundColor: Color(0xFFE53E3E),
          elevation: 0,
          padding: EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: Color(0xFFE53E3E).withOpacity(0.3),
              width: 1,
            ),
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

class ProfileOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback? onTap;
  final bool isDarkMode;

  const ProfileOption({
    required this.icon,
    required this.title,
    required this.isDarkMode,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      leading: Icon(
        icon,
        color: isDarkMode ? Color(0xFF4A90E2) : Color(0xFF64748B),
        size: 22,
      ),
      title: Text(
        title,
        style: GoogleFonts.poppins(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Color(0xFF2D3748),
        ),
      ),
      trailing: Icon(
        Icons.chevron_right_rounded,
        size: 20,
        color: isDarkMode ? Colors.white60 : Color(0xFF94A3B8),
      ),
      contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }
}
